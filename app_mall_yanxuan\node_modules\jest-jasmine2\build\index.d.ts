/**
 * Copyright (c) Facebook, Inc. and its affiliates. All Rights Reserved.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */
import type { Config } from '@jest/types';
import type { TestResult } from '@jest/test-result';
import type { JestEnvironment } from '@jest/environment';
import type { RuntimeType as Runtime } from 'jest-runtime';
import type { Jasmine as JestJasmine } from './types';
declare function jasmine2(globalConfig: Config.GlobalConfig, config: Config.ProjectConfig, environment: JestEnvironment, runtime: Runtime, testPath: string): Promise<TestResult>;
declare namespace jasmine2 {
    type Jasmine = JestJasmine;
}
export = jasmine2;
