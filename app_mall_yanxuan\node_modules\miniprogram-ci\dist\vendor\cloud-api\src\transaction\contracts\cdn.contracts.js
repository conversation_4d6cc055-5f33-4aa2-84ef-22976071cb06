!function(require, directRequire){
"use strict";Object.defineProperty(exports,"__esModule",{value:!0}),exports.cdnTcbCheckResourceContract=exports.cdnTcbModifyAttributeContract=void 0;const tslib_1=require("tslib"),factory_1=tslib_1.__importDefault(require("./factory")),validations=tslib_1.__importStar(require("../validations/validations")),transactor_1=require("../transactor");function sharedInputTransformation(t,r){return(t&&r===transactor_1.TransactType.HTTP||r===transactor_1.TransactType.IDEPlugin||r===transactor_1.TransactType.IDE)&&(delete t.Action,delete t.Version,delete t.Region),JSON.stringify(t)}function sharedOutputTransformationThrows(t,r){if(!(t=JSON.parse(t))||!t.Response)throw new Error("content empty, "+JSON.stringify(t));const e=t.Response;if(e.Error&&e.Error.Code){const t=new Error(e.Error.Code+", "+e.Error.Message+" ("+(e.RequestId||"?")+")");throw t.code=e.Error.Code,t}return delete e.Error,e}exports.cdnTcbModifyAttributeContract=new factory_1.default("ITCCDNTcbModifyAttributeInput","ITCCDNTcbModifyAttributeOutput",validations.cdnTcbModifyAttributeOutputValidation,()=>({cgi_id:-1,service:"cdn",action:"TcbModifyAttribute",version:"2018-06-06",region:""}),{inputTransformation:sharedInputTransformation,outputTransformationThrows:sharedOutputTransformationThrows}),exports.cdnTcbCheckResourceContract=new factory_1.default("ITCCDNTcbCheckResourceInput","ITCCDNTcbCheckResourceOutput",validations.cdnTcbCheckResourceOutputValidation,()=>({cgi_id:-1,service:"cdn",action:"TcbCheckResource",version:"2018-06-06",region:""}),{inputTransformation:sharedInputTransformation,outputTransformationThrows:sharedOutputTransformationThrows});
}(require("licia/lazyImport")(require), require)