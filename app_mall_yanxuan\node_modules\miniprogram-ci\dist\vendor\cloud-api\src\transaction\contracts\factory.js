!function(require, directRequire){
"use strict";Object.defineProperty(exports,"__esModule",{value:!0}),exports.allContractRecorder=exports.allContractsRecords=void 0;const validator_1=require("../../utils/validator"),eventemitter3_1=require("eventemitter3");exports.allContractsRecords=[],exports.allContractRecorder=new eventemitter3_1.EventEmitter,"undefined"!=typeof window&&"undefined"==typeof global&&(window.global={}),global.env&&global.env.isDev&&global.env.transact&&(global.allContractsRecords=exports.allContractsRecords);const noop=(...t)=>{};class TransactionContract extends eventemitter3_1.EventEmitter{constructor(t,o,e,r,n={}){super(),this.inputTypeName=t,this.outputTypeName=o,this.outputValidation=e,this.getHttpAgentIdentity=r,this.options=n,this.validator=new validator_1.Validator(o,e)}outputTransformationThrows(t,o){return this.options.outputTransformationThrows?this.options.outputTransformationThrows(t,o):t}inputTransformation(t,o){return this.options.inputTransformation?this.options.inputTransformation(t,o):t}validOutput(t){try{return this.validator.validThrows(t),!0}catch(t){return!1}}validOutputThrows(t){this.validator.validThrows(t)}commitRecord(t,o){if(!global.env||!global.env.transact)return;const e={timestamps:t.timestamps,rawOutput:t.rawOutput,input:t.input,maybeBrokenOutput:t.output,error:t.error,isPoll:Boolean(o),httpAgentIdentity:t.httpAgentIdentity};this.emit("record",e);const r=Object.assign({inputName:this.inputTypeName},e);exports.allContractsRecords.push(r),exports.allContractRecorder.emit("record",r)}}exports.default=TransactionContract;
}(require("licia/lazyImport")(require), require)