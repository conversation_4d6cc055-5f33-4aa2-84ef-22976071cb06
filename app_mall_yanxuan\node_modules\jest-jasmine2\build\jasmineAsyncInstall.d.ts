/**
 * Copyright (c) Facebook, Inc. and its affiliates. All Rights Reserved.
 *
 * This source code is licensed under the MIT license found in the
 * LICENSE file in the root directory of this source tree.
 */
/**
 * This module adds ability to test async promise code with jasmine by
 * returning a promise from `it/test` and `before/afterEach/All` blocks.
 */
import type { Config, Global } from '@jest/types';
export default function jasmineAsyncInstall(globalConfig: Config.GlobalConfig, global: Global.Global): void;
