{"version": 3, "file": "camera-selection-ui.js", "sourceRoot": "", "sources": ["../../../../src/ui/scanner/camera-selection-ui.ts"], "names": [], "mappings": ";;;AAWA,+BAGgB;AAChB,yCAEuB;AAGvB;IAMI,2BAAoB,OAA4B;QAC5C,IAAI,CAAC,aAAa,GAAG,2BAAoB;aACpC,aAAa,CACd,QAAQ,EACR,kCAA2B,CAAC,0BAA0B,CAAC,CAAC;QAC5D,IAAI,CAAC,OAAO,GAAG,OAAO,CAAC;QACvB,IAAI,CAAC,OAAO,GAAG,EAAE,CAAC;IACtB,CAAC;IAGO,kCAAM,GAAd,UACI,aAA0B;QAC1B,IAAM,wBAAwB,GAAG,QAAQ,CAAC,aAAa,CAAC,MAAM,CAAC,CAAC;QAChE,wBAAwB,CAAC,KAAK,CAAC,WAAW,GAAG,MAAM,CAAC;QACpD,IAAM,UAAU,GAAG,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC;QACvC,IAAI,UAAU,KAAK,CAAC,EAAE;YAClB,MAAM,IAAI,KAAK,CAAC,kBAAkB,CAAC,CAAC;SACvC;QACD,IAAI,UAAU,KAAK,CAAC,EAAE;YAElB,wBAAwB,CAAC,KAAK,CAAC,OAAO,GAAG,MAAM,CAAC;SACnD;aAAM;YAEH,IAAM,kBAAkB,GAAG,mCAAyB,CAAC,YAAY,EAAE,CAAC;YACpE,wBAAwB,CAAC,SAAS;kBAC5B,UAAG,kBAAkB,eAAK,IAAI,CAAC,OAAO,CAAC,MAAM,QAAK,CAAC;SAC5D;QAED,IAAI,iBAAiB,GAAG,CAAC,CAAC;QAE1B,KAAqB,UAAY,EAAZ,KAAA,IAAI,CAAC,OAAO,EAAZ,cAAY,EAAZ,IAAY,EAAE;YAA9B,IAAM,MAAM,SAAA;YACb,IAAM,KAAK,GAAG,MAAM,CAAC,EAAE,CAAC;YACxB,IAAI,MAAI,GAAG,MAAM,CAAC,KAAK,IAAI,IAAI,CAAC,CAAC,CAAC,KAAK,CAAC,CAAC,CAAC,MAAM,CAAC,KAAK,CAAC;YAGvD,IAAI,CAAC,MAAI,IAAI,MAAI,KAAK,EAAE,EAAE;gBACtB,MAAI,GAAG;oBACH,mCAAyB,CAAC,qBAAqB,EAAE;oBACjD,iBAAiB,EAAE;iBAClB,CAAC,IAAI,CAAC,GAAG,CAAC,CAAC;aACnB;YAED,IAAM,MAAM,GAAG,QAAQ,CAAC,aAAa,CAAC,QAAQ,CAAC,CAAC;YAChD,MAAM,CAAC,KAAK,GAAG,KAAK,CAAC;YACrB,MAAM,CAAC,SAAS,GAAG,MAAI,CAAC;YACxB,IAAI,CAAC,OAAO,CAAC,IAAI,CAAC,MAAM,CAAC,CAAC;YAC1B,IAAI,CAAC,aAAa,CAAC,WAAW,CAAC,MAAM,CAAC,CAAC;SAC1C;QACD,wBAAwB,CAAC,WAAW,CAAC,IAAI,CAAC,aAAa,CAAC,CAAC;QACzD,aAAa,CAAC,WAAW,CAAC,wBAAwB,CAAC,CAAC;IACxD,CAAC;IAGM,mCAAO,GAAd;QACI,IAAI,CAAC,aAAa,CAAC,QAAQ,GAAG,IAAI,CAAC;IACvC,CAAC;IAEM,sCAAU,GAAjB;QACI,OAAO,IAAI,CAAC,aAAa,CAAC,QAAQ,KAAK,IAAI,CAAC;IAChD,CAAC;IAEM,kCAAM,GAAb;QACI,IAAI,CAAC,aAAa,CAAC,QAAQ,GAAG,KAAK,CAAC;IACxC,CAAC;IAEM,oCAAQ,GAAf;QACI,OAAO,IAAI,CAAC,aAAa,CAAC,KAAK,CAAC;IACpC,CAAC;IAEM,oCAAQ,GAAf,UAAgB,KAAa;QACzB,KAAqB,UAAY,EAAZ,KAAA,IAAI,CAAC,OAAO,EAAZ,cAAY,EAAZ,IAAY,EAAE;YAA9B,IAAM,MAAM,SAAA;YACb,IAAI,MAAM,CAAC,KAAK,KAAK,KAAK,EAAE;gBACxB,OAAO,IAAI,CAAC;aACf;SACJ;QACD,OAAO,KAAK,CAAC;IACjB,CAAC;IAEM,oCAAQ,GAAf,UAAgB,KAAa;QACzB,IAAI,CAAC,IAAI,CAAC,QAAQ,CAAC,KAAK,CAAC,EAAE;YACvB,MAAM,IAAI,KAAK,CAAC,UAAG,KAAK,wCAAqC,CAAC,CAAC;SAClE;QACD,IAAI,CAAC,aAAa,CAAC,KAAK,GAAG,KAAK,CAAC;IACrC,CAAC;IAEM,yCAAa,GAApB;QACI,OAAO,IAAI,CAAC,OAAO,CAAC,MAAM,KAAK,CAAC,CAAC;IACrC,CAAC;IAEM,sCAAU,GAAjB;QACI,OAAO,IAAI,CAAC,OAAO,CAAC,MAAM,CAAC;IAC/B,CAAC;IAIa,wBAAM,GAApB,UACI,aAA0B,EAC1B,OAA4B;QAC5B,IAAI,cAAc,GAAG,IAAI,iBAAiB,CAAC,OAAO,CAAC,CAAC;QACpD,cAAc,CAAC,MAAM,CAAC,aAAa,CAAC,CAAC;QACrC,OAAO,cAAc,CAAC;IAC1B,CAAC;IACL,wBAAC;AAAD,CAAC,AA5GD,IA4GC;AA5GY,8CAAiB"}