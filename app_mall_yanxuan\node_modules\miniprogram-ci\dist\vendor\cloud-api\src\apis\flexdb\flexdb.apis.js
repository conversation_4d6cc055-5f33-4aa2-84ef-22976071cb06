!function(require, directRequire){
"use strict";Object.defineProperty(exports,"__esModule",{value:!0}),exports.flexdbModifyNameSpace=exports.flexdbDescribeRestoreTime=exports.flexdbRestoreTCBTables=exports.flexdbDescribeRestoreTask=exports.flexdbDescribeRestoreTables=exports.flexdbModifyTableNames=exports.flexdbRunCommands=exports.flexdbUpdateTable=exports.flexdbDescribeTable=exports.flexdbCount=exports.flexdbDeleteItem=exports.flexdbUpdateItem=exports.flexdbPutItem=exports.flexdbQuery=exports.flexdbDeleteTable=exports.flexdbCreateTable=exports.flexdbListTables=void 0;const tslib_1=require("tslib"),transactor_1=tslib_1.__importDefault(require("../../transaction/transactor")),contracts_1=require("../../transaction/contracts/contracts"),flexdbContracts=tslib_1.__importStar(require("../../transaction/contracts/contracts"));async function flexdbListTables(e){const t={Action:"ListTables",Version:"2018-11-27",Region:e.region,Tag:e.tag,MgoLimit:e.mgoLimit,MgoOffset:e.mgoOffset};try{const e=await(0,transactor_1.default)(contracts_1.flexdbListTablesContract,t);return{tables:e.Tables?e.Tables.map(e=>({tableName:e.TableName,count:e.Count,size:e.Size,indexCount:e.IndexCount,indexSize:e.IndexSize})):[],pager:{offset:e.Pager.Offset,limit:e.Pager.Limit,total:e.Pager.Total}}}catch(e){throw e}}async function flexdbCreateTable(e){const t={Action:"CreateTable",Version:"2018-11-27",Region:e.region,Tag:e.tag,TableName:e.tableName};try{return await(0,transactor_1.default)(contracts_1.flexdbCreateTableContract,t),{}}catch(e){throw e}}async function flexdbDeleteTable(e){const t={Action:"DeleteTable",Version:"2018-11-27",Region:e.region,Tag:e.tag,TableName:e.tableName};try{return await(0,transactor_1.default)(contracts_1.flexdbDeleteTableContract,t),{}}catch(e){throw e}}async function flexdbQuery(e){const t={Action:"Query",Version:"2018-11-27",Region:e.region,Tag:e.tag,TableName:e.tableName,MgoLimit:e.mgoLimit,MgoProjection:e.mgoProjection,MgoQuery:e.mgoQuery,MgoOffset:e.mgoOffset,MgoSort:e.mgoSort};try{const e=await(0,transactor_1.default)(contracts_1.flexdbQueryContract,t);return{data:e.Data?e.Data.map(e=>JSON.parse(e)):[],pager:{offset:e.Pager.Offset,limit:e.Pager.Limit,total:e.Pager.Total}}}catch(e){throw e}}async function flexdbPutItem(e){const t={Action:"PutItem",Version:"2018-11-27",Region:e.region,Tag:e.tag,TableName:e.tableName,MgoDocs:e.mgoDocs};try{return{insertedIds:(await(0,transactor_1.default)(contracts_1.flexdbPutItemContract,t)).InsertedIds}}catch(e){throw e}}async function flexdbUpdateItem(e){const t={Action:"UpdateItem",Version:"2018-11-27",Region:e.region,Tag:e.tag,TableName:e.tableName,MgoUpdate:e.mgoUpdate,MgoUpsert:e.mgoUpsert,MgoIsMulti:e.mgoIsMulti,MgoQuery:e.mgoQuery};try{const e=await(0,transactor_1.default)(contracts_1.flexdbUpdateItemContract,t);return{modifiedNum:e.ModifiedNum,matchedNum:e.MatchedNum,upsertedId:e.UpsertedId}}catch(e){throw e}}async function flexdbDeleteItem(e){const t={Action:"DeleteItem",Version:"2018-11-27",Region:e.region,Tag:e.tag,TableName:e.tableName,MgoIsMulti:e.mgoIsMulti,MgoQuery:e.mgoQuery};try{return{deleted:(await(0,transactor_1.default)(contracts_1.flexdbDeleteItemContract,t)).Deleted}}catch(e){throw e}}async function flexdbCount(e){const t={Action:"Count",Version:"2018-11-27",Region:e.region,Tag:e.tag,TableName:e.tableName,MgoQuery:e.mgoQuery};try{return{count:(await(0,transactor_1.default)(contracts_1.flexdbCountContract,t)).Count}}catch(e){throw e}}async function flexdbDescribeTable(e){const t={Action:"DescribeTable",Version:"2018-11-27",Region:e.region,Tag:e.tag,TableName:e.tableName};try{const e=await(0,transactor_1.default)(contracts_1.flexdbDescribeTableContract,t);return{indexes:e.Indexes.map(e=>({name:e.Name,size:e.Size,keys:e.Keys.map(e=>({name:e.Name,direction:e.Direction})),accesses:{ops:e.Accesses.Ops,since:e.Accesses.Since},unique:e.Unique})),indexNum:e.IndexNum}}catch(e){throw e}}async function flexdbUpdateTable(e){const t={Action:"UpdateTable",Version:"2018-11-27",Region:e.region,Tag:e.tag,TableName:e.tableName,CreateIndexes:(e.createIndexes||[]).map(e=>({IndexName:e.indexName,MgoKeySchema:{MgoIndexKeys:e.mgoKeySchema.mgoIndexKeys.map(e=>({Name:e.name,Direction:e.direction})),MgoIsUnique:e.mgoKeySchema.mgoIsUnique}})),DropIndexes:(e.dropIndexes||[]).map(e=>({IndexName:e.indexName}))};try{return await(0,transactor_1.default)(contracts_1.flexdbUpdateTableContract,t),{}}catch(e){throw e}}async function flexdbRunCommands(e){const t={Action:"RunCommands",Version:"2018-11-27",Region:e.region,Tag:e.tag,MgoCommands:e.mgoCommands.map(e=>({TableName:e.tableName,CommandType:e.commandType,Command:e.command}))};try{return{data:(await(0,transactor_1.default)(contracts_1.flexdbRunCommandsContract,t)).Data}}catch(e){throw e}}async function flexdbModifyTableNames(e){const t={Action:"ModifyTableNames",Version:"2018-11-27",Region:e.region,InstanceId:e.instanceId,ModifyTableInfo:e.modifyTableInfo.map(e=>({OldTableName:e.oldTableName,NewTableName:e.newTableName}))};try{return{flowId:(await(0,transactor_1.default)(contracts_1.flexdbModifyTableNamesContract,t)).FlowId}}catch(e){throw e}}async function flexdbDescribeRestoreTables(e){const t={Action:"DescribeRestoreTables",Version:"2018-11-27",Region:e.region,InstanceId:e.instanceId,Time:e.time};return{tables:(await(0,transactor_1.default)(contracts_1.flexdbDescribeRestoreTablesContract,t)).Tables}}async function flexdbDescribeRestoreTask(e){const t={Action:"DescribeRestoreTask",Version:"2018-11-27",Region:e.region,InstanceId:e.instanceId};return{tasks:(await(0,transactor_1.default)(contracts_1.flexdbDescribeRestoreTaskContract,t)).Tasks.map(e=>({tables:(e.Tables||[]).map(e=>({oldTableName:e.OldTableName,newTableName:e.NewTableName})),createTime:e.CreateTime,status:e.Status,endTime:e.EndTime,restoreTime:e.RestoreTime}))}}async function flexdbRestoreTCBTables(e){const t={Action:"RestoreTCBTables",Version:"2018-11-27",Region:e.region,InstanceId:e.instanceId,Time:e.time,ModifyTableNamesInfo:e.modifyTableNamesInfo.map(e=>({OldTableName:e.oldTableName,NewTableName:e.newTableName}))};return{flowId:(await(0,transactor_1.default)(flexdbContracts.flexdbRestoreTCBTablesContract,t)).FlowId}}async function flexdbDescribeRestoreTime(e){const t={Action:"DescribeRestoreTime",Version:"2018-11-27",Region:e.region,InstanceId:e.instanceId},a=await(0,transactor_1.default)(flexdbContracts.flexdbDescribeRestoreTimeContract,t);return{restoreTimes:a.RestoreTimes,restoreTimeRanges:[{startTime:a.RestoreTimeRanges[0].StartTime,endTime:a.RestoreTimeRanges[0].EndTime}]}}async function flexdbModifyNameSpace(e){const t={Action:"ModifyNameSpace",Version:"2018-11-27",Region:e.region,Tag:e.tag,ModifyTableInfo:e.modifyTableInfo.map(e=>({OldTableName:e.oldTableName,NewTableName:e.newTableName}))};return{flowId:(await(0,transactor_1.default)(flexdbContracts.flexdbModifyNameSpaceContract,t)).FlowId}}exports.flexdbListTables=flexdbListTables,exports.flexdbCreateTable=flexdbCreateTable,exports.flexdbDeleteTable=flexdbDeleteTable,exports.flexdbQuery=flexdbQuery,exports.flexdbPutItem=flexdbPutItem,exports.flexdbUpdateItem=flexdbUpdateItem,exports.flexdbDeleteItem=flexdbDeleteItem,exports.flexdbCount=flexdbCount,exports.flexdbDescribeTable=flexdbDescribeTable,exports.flexdbUpdateTable=flexdbUpdateTable,exports.flexdbRunCommands=flexdbRunCommands,exports.flexdbModifyTableNames=flexdbModifyTableNames,exports.flexdbDescribeRestoreTables=flexdbDescribeRestoreTables,exports.flexdbDescribeRestoreTask=flexdbDescribeRestoreTask,exports.flexdbRestoreTCBTables=flexdbRestoreTCBTables,exports.flexdbDescribeRestoreTime=flexdbDescribeRestoreTime,exports.flexdbModifyNameSpace=flexdbModifyNameSpace;
}(require("licia/lazyImport")(require), require)