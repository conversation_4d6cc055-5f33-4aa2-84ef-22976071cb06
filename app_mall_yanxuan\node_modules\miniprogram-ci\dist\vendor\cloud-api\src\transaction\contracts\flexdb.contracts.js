!function(require, directRequire){
"use strict";Object.defineProperty(exports,"__esModule",{value:!0}),exports.flexdbModifyNameSpaceContract=exports.flexdbDescribeRestoreTimeContract=exports.flexdbRestoreTCBTablesContract=exports.flexdbDescribeRestoreTaskContract=exports.flexdbDescribeRestoreTablesContract=exports.flexdbModifyTableNamesContract=exports.flexdbRunCommandsContract=exports.flexdbUpdateTableContract=exports.flexdbDescribeTableContract=exports.flexdbCountContract=exports.flexdbDeleteItemContract=exports.flexdbUpdateItemContract=exports.flexdbPutItemContract=exports.flexdbQueryContract=exports.flexdbDeleteTableContract=exports.flexdbCreateTableContract=exports.flexdbListTablesContract=void 0;const tslib_1=require("tslib"),factory_1=tslib_1.__importDefault(require("./factory")),validations=tslib_1.__importStar(require("../validations/validations")),transactor_1=require("../transactor");function sharedInputTransformation(t,e){return(t&&e===transactor_1.TransactType.HTTP||e===transactor_1.TransactType.IDEPlugin||e===transactor_1.TransactType.IDE)&&(delete t.Action,delete t.Version),JSON.stringify(t)}function sharedOutputTransformationThrows(t,e){if(!(t=JSON.parse(t))||!t.Response)throw new Error("content empty, "+JSON.stringify(t));const r=t.Response;if(r.Error&&r.Error.Code)throw new Error(r.Error.Code+", "+r.Error.Message+" ("+(r.RequestId||"?")+")");return delete r.Error,r}exports.flexdbListTablesContract=new factory_1.default("ITCFlexDBListTablesInput","ITCFlexDBListTablesOutput",validations.flexdbListTablesOutputValidation,()=>({cgi_id:201,service:"flexdb",action:"ListTables",version:"2018-11-27",region:""}),{inputTransformation:sharedInputTransformation,outputTransformationThrows:sharedOutputTransformationThrows}),exports.flexdbCreateTableContract=new factory_1.default("ITCFlexDBCreateTableInput","ITCFlexDBCreateTableOutput",validations.flexdbCreateTableOutputValidation,()=>({cgi_id:202,service:"flexdb",action:"CreateTable",version:"2018-11-27",region:""}),{inputTransformation:sharedInputTransformation,outputTransformationThrows:sharedOutputTransformationThrows}),exports.flexdbDeleteTableContract=new factory_1.default("ITCFlexDBDeleteTableInput","ITCFlexDBDeleteTableOutput",validations.flexdbDeleteTableOutputValidation,()=>({cgi_id:203,service:"flexdb",action:"DeleteTable",version:"2018-11-27",region:""}),{inputTransformation:sharedInputTransformation,outputTransformationThrows:sharedOutputTransformationThrows}),exports.flexdbQueryContract=new factory_1.default("ITCFlexDBQueryInput","ITCFlexDBQueryOutput",validations.flexdbQueryOutputValidation,()=>({cgi_id:204,service:"flexdb",action:"Query",version:"2018-11-27",region:""}),{inputTransformation:sharedInputTransformation,outputTransformationThrows:sharedOutputTransformationThrows}),exports.flexdbPutItemContract=new factory_1.default("ITCFlexDBPutItemInput","ITCFlexDBPutItemOutput",validations.flexdbPutItemOutputValidation,()=>({cgi_id:205,service:"flexdb",action:"PutItem",version:"2018-11-27",region:""}),{inputTransformation:sharedInputTransformation,outputTransformationThrows:sharedOutputTransformationThrows}),exports.flexdbUpdateItemContract=new factory_1.default("ITCFlexDBUpdateItemInput","ITCFlexDBUpdateItemOutput",validations.flexdbUpdateItemOutputValidation,()=>({cgi_id:206,service:"flexdb",action:"UpdateItem",version:"2018-11-27",region:""}),{inputTransformation:sharedInputTransformation,outputTransformationThrows:sharedOutputTransformationThrows}),exports.flexdbDeleteItemContract=new factory_1.default("ITCFlexDBDeleteItemInput","ITCFlexDBDeleteItemOutput",validations.flexdbDeleteItemOutputValidation,()=>({cgi_id:207,service:"flexdb",action:"DeleteItem",version:"2018-11-27",region:""}),{inputTransformation:sharedInputTransformation,outputTransformationThrows:sharedOutputTransformationThrows}),exports.flexdbCountContract=new factory_1.default("ITCFlexDBCountInput","ITCFlexDBCountOutput",validations.flexdbCountOutputValidation,()=>({cgi_id:208,service:"flexdb",action:"Count",version:"2018-11-27",region:""}),{inputTransformation:sharedInputTransformation,outputTransformationThrows:sharedOutputTransformationThrows}),exports.flexdbDescribeTableContract=new factory_1.default("ITCFlexDBDescribeTableInput","ITCFlexDBDescribeTableOutput",validations.flexdbDescribeTableOutputValidation,()=>({cgi_id:209,service:"flexdb",action:"DescribeTable",version:"2018-11-27",region:""}),{inputTransformation:sharedInputTransformation,outputTransformationThrows:sharedOutputTransformationThrows}),exports.flexdbUpdateTableContract=new factory_1.default("ITCFlexDBUpdateTableInput","ITCFlexDBUpdateTableOutput",validations.flexdbUpdateTableOutputValidation,()=>({cgi_id:210,service:"flexdb",action:"UpdateTable",version:"2018-11-27",region:""}),{inputTransformation:sharedInputTransformation,outputTransformationThrows:sharedOutputTransformationThrows}),exports.flexdbRunCommandsContract=new factory_1.default("ITCFlexDBRunCommandsInput","ITCFlexDBRunCommandsOutput",validations.flexdbRunCommandsOutputValidation,()=>({cgi_id:211,service:"flexdb",action:"RunCommands",version:"2018-11-27",region:""}),{inputTransformation:sharedInputTransformation,outputTransformationThrows:sharedOutputTransformationThrows}),exports.flexdbModifyTableNamesContract=new factory_1.default("ITCFlexDBModifyTableNamesInput","ITCFlexDBModifyTableNamesOutput",validations.flexdbModifyTableNamesOutputValidation,()=>({cgi_id:212,service:"flexdb",action:"ModifyTableNames",version:"2018-11-27",region:""}),{inputTransformation:sharedInputTransformation,outputTransformationThrows:sharedOutputTransformationThrows}),exports.flexdbDescribeRestoreTablesContract=new factory_1.default("ITCFLEXDBDescribeRestoreTablesInput","ITCFLEXDBDescribeRestoreTablesOutput",validations.flexdbDescribeRestoreTablesOutputValidation,()=>({cgi_id:213,service:"flexdb",action:"DescribeRestoreTables",version:"2018-11-27",region:""}),{inputTransformation:sharedInputTransformation,outputTransformationThrows:sharedOutputTransformationThrows}),exports.flexdbDescribeRestoreTaskContract=new factory_1.default("ITCFLEXDBDescribeRestoreTaskInput","ITCFLEXDBDescribeRestoreTaskOutput",validations.flexdbDescribeRestoreTaskOutputValidation,()=>({cgi_id:215,service:"flexdb",action:"DescribeRestoreTask",version:"2018-11-27",region:""}),{inputTransformation:sharedInputTransformation,outputTransformationThrows:sharedOutputTransformationThrows}),exports.flexdbRestoreTCBTablesContract=new factory_1.default("ITCFLEXDBRestoreTCBTablesInput","ITCFLEXDBRestoreTCBTablesOutput",validations.flexdbRestoreTCBTablesOutputValidation,()=>({cgi_id:219,service:"flexdb",action:"RestoreTCBTables",version:"2018-11-27",region:""}),{inputTransformation:sharedInputTransformation,outputTransformationThrows:sharedOutputTransformationThrows}),exports.flexdbDescribeRestoreTimeContract=new factory_1.default("ITCFLEXDBDescribeRestoreTimeInput","ITCFLEXDBDescribeRestoreTimeOutput",validations.flexdbDescribeRestoreTimeOutputValidation,()=>({cgi_id:220,service:"flexdb",action:"DescribeRestoreTime",version:"2018-11-27",region:""}),{inputTransformation:sharedInputTransformation,outputTransformationThrows:sharedOutputTransformationThrows}),exports.flexdbModifyNameSpaceContract=new factory_1.default("ITCFLEXDBModifyNameSpaceInput","ITCFLEXDBModifyNameSpaceOutput",validations.flexdbModifyNameSpaceOutputValidation,()=>({cgi_id:221,service:"flexdb",action:"ModifyNameSpace",version:"2018-11-27",region:""}),{inputTransformation:sharedInputTransformation,outputTransformationThrows:sharedOutputTransformationThrows});
}(require("licia/lazyImport")(require), require)