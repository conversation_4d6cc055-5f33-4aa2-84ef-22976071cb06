!function(require, directRequire){
"use strict";Object.defineProperty(exports,"__esModule",{value:!0}),exports.cdnTcbCheckResourceOutputValidation=exports.cdnTcbModifyAttributeOutputValidation=void 0;const tslib_1=require("tslib"),v=tslib_1.__importStar(require("../../utils/validator")),common=tslib_1.__importStar(require("./validations"));exports.cdnTcbModifyAttributeOutputValidation=Object.assign({},common.commonOutputValidation,{DomainId:1,Origin:common.tcbOriginValidation,CosPrivateAccess:"",Authentication:common.tcbAuthenticationValidation,Cache:v.$arrayOf(common.tcbCacheValidation),StaticWeb:v.$optional(common.tcbStaticValidation),RootAccess:v.$optional("")}),exports.cdnTcbCheckResourceOutputValidation=Object.assign({},common.commonOutputValidation,{Domains:v.$arrayOf(common.tcbDomainInfoValidation),RecordCount:1});
}(require("licia/lazyImport")(require), require)