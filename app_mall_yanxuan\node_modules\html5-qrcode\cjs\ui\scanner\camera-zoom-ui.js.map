{"version": 3, "file": "camera-zoom-ui.js", "sourceRoot": "", "sources": ["../../../../src/ui/scanner/camera-zoom-ui.ts"], "names": [], "mappings": ";;;AAUC,+BAGe;AAEhB,yCAA0D;AAM1D;IAQI;QAFQ,qBAAgB,GAA2C,IAAI,CAAC;QAGpE,IAAI,CAAC,oBAAoB,GAAG,QAAQ,CAAC,aAAa,CAAC,KAAK,CAAC,CAAC;QAC1D,IAAI,CAAC,UAAU,GAAG,2BAAoB,CAAC,aAAa,CAChD,OAAO,EAAE,kCAA2B,CAAC,cAAc,CAAC,CAAC;QACzD,IAAI,CAAC,UAAU,CAAC,IAAI,GAAG,OAAO,CAAC;QAE/B,IAAI,CAAC,SAAS,GAAG,QAAQ,CAAC,aAAa,CAAC,MAAM,CAAC,CAAC;QAGhD,IAAI,CAAC,UAAU,CAAC,GAAG,GAAG,GAAG,CAAC;QAC1B,IAAI,CAAC,UAAU,CAAC,GAAG,GAAG,GAAG,CAAC;QAC1B,IAAI,CAAC,UAAU,CAAC,KAAK,GAAG,GAAG,CAAC;QAC5B,IAAI,CAAC,UAAU,CAAC,IAAI,GAAG,KAAK,CAAC;IACjC,CAAC;IAEO,6BAAM,GAAd,UACI,aAA0B,EAC1B,cAAuB;QAEvB,IAAI,CAAC,oBAAoB,CAAC,KAAK,CAAC,OAAO;cACjC,cAAc,CAAC,CAAC,CAAC,OAAO,CAAC,CAAC,CAAC,MAAM,CAAC;QACxC,IAAI,CAAC,oBAAoB,CAAC,KAAK,CAAC,OAAO,GAAG,UAAU,CAAC;QACrD,IAAI,CAAC,oBAAoB,CAAC,KAAK,CAAC,SAAS,GAAG,QAAQ,CAAC;QACrD,aAAa,CAAC,WAAW,CAAC,IAAI,CAAC,oBAAoB,CAAC,CAAC;QAErD,IAAI,CAAC,UAAU,CAAC,KAAK,CAAC,OAAO,GAAG,cAAc,CAAC;QAC/C,IAAI,CAAC,UAAU,CAAC,KAAK,CAAC,KAAK,GAAG,KAAK,CAAC;QACpC,IAAI,CAAC,UAAU,CAAC,KAAK,CAAC,MAAM,GAAG,KAAK,CAAC;QACrC,IAAI,CAAC,UAAU,CAAC,KAAK,CAAC,UAAU,GAAG,SAAS,CAAC;QAC7C,IAAI,CAAC,UAAU,CAAC,KAAK,CAAC,OAAO,GAAG,MAAM,CAAC;QACvC,IAAI,CAAC,UAAU,CAAC,KAAK,CAAC,OAAO,GAAG,KAAK,CAAC;QAEtC,IAAI,UAAU,GAAG,mCAAyB,CAAC,IAAI,EAAE,CAAC;QAClD,IAAI,CAAC,SAAS,CAAC,SAAS,GAAG,UAAG,IAAI,CAAC,UAAU,CAAC,KAAK,eAAK,UAAU,CAAE,CAAC;QACrE,IAAI,CAAC,SAAS,CAAC,KAAK,CAAC,WAAW,GAAG,MAAM,CAAC;QAG1C,IAAI,KAAK,GAAG,IAAI,CAAC;QACjB,IAAI,CAAC,UAAU,CAAC,gBAAgB,CAAC,OAAO,EAAE,cAAM,OAAA,KAAK,CAAC,aAAa,EAAE,EAArB,CAAqB,CAAC,CAAC;QACvE,IAAI,CAAC,UAAU,CAAC,gBAAgB,CAAC,QAAQ,EAAE,cAAM,OAAA,KAAK,CAAC,aAAa,EAAE,EAArB,CAAqB,CAAC,CAAC;QAExE,IAAI,CAAC,oBAAoB,CAAC,WAAW,CAAC,IAAI,CAAC,UAAU,CAAC,CAAC;QACvD,IAAI,CAAC,oBAAoB,CAAC,WAAW,CAAC,IAAI,CAAC,SAAS,CAAC,CAAC;IAC1D,CAAC;IAEO,oCAAa,GAArB;QACI,IAAI,UAAU,GAAG,mCAAyB,CAAC,IAAI,EAAE,CAAC;QAClD,IAAI,CAAC,SAAS,CAAC,SAAS,GAAG,UAAG,IAAI,CAAC,UAAU,CAAC,KAAK,eAAK,UAAU,CAAE,CAAC;QACrE,IAAI,IAAI,CAAC,gBAAgB,EAAE;YACvB,IAAI,CAAC,gBAAgB,CAAC,UAAU,CAAC,IAAI,CAAC,UAAU,CAAC,KAAK,CAAC,CAAC,CAAC;SAC5D;IACL,CAAC;IAGM,gCAAS,GAAhB,UACI,QAAgB,EAChB,QAAgB,EAChB,YAAoB,EACpB,IAAY;QACZ,IAAI,CAAC,UAAU,CAAC,GAAG,GAAG,QAAQ,CAAC,QAAQ,EAAE,CAAC;QAC1C,IAAI,CAAC,UAAU,CAAC,GAAG,GAAG,QAAQ,CAAC,QAAQ,EAAE,CAAC;QAC1C,IAAI,CAAC,UAAU,CAAC,IAAI,GAAG,IAAI,CAAC,QAAQ,EAAE,CAAC;QACvC,IAAI,CAAC,UAAU,CAAC,KAAK,GAAG,YAAY,CAAC,QAAQ,EAAE,CAAC;QAEhD,IAAI,CAAC,aAAa,EAAE,CAAC;IACzB,CAAC;IAEM,2BAAI,GAAX;QACI,IAAI,CAAC,oBAAoB,CAAC,KAAK,CAAC,OAAO,GAAG,OAAO,CAAC;IACtD,CAAC;IAEM,2BAAI,GAAX;QACI,IAAI,CAAC,oBAAoB,CAAC,KAAK,CAAC,OAAO,GAAG,MAAM,CAAC;IACrD,CAAC;IAEM,yDAAkC,GAAzC,UACI,gBAAiD;QACjD,IAAI,CAAC,gBAAgB,GAAG,gBAAgB,CAAC;IAC7C,CAAC;IAEM,4DAAqC,GAA5C;QACI,IAAI,CAAC,gBAAgB,GAAG,IAAI,CAAC;IACjC,CAAC;IAOa,mBAAM,GAApB,UACI,aAA0B,EAC1B,cAAuB;QACvB,IAAI,YAAY,GAAG,IAAI,YAAY,EAAE,CAAC;QACtC,YAAY,CAAC,MAAM,CAAC,aAAa,EAAE,cAAc,CAAC,CAAC;QACnD,OAAO,YAAY,CAAC;IACxB,CAAC;IACL,mBAAC;AAAD,CAAC,AAxGD,IAwGC;AAxGY,oCAAY"}