!function(require, directRequire){
"use strict";Object.defineProperty(exports,"__esModule",{value:!0}),exports.flexdbModifyNameSpaceOutputValidation=exports.flexdbDescribeRestoreTimeOutputValidation=exports.flexdbRestoreTCBTablesOutputValidation=exports.flexdbDescribeRestoreTaskOutputValidation=exports.flexdbDescribeRestoreTablesOutputValidation=exports.flexdbModifyTableNamesOutputValidation=exports.flexdbRunCommandsOutputValidation=exports.flexdbUpdateTableOutputValidation=exports.flexdbDescribeTableOutputValidation=exports.flexdbCountOutputValidation=exports.flexdbDeleteItemOutputValidation=exports.flexdbUpdateItemOutputValidation=exports.flexdbPutItemOutputValidation=exports.flexdbQueryOutputValidation=exports.flexdbDeleteTableOutputValidation=exports.flexdbCreateTableOutputValidation=exports.flexdbListTablesOutputValidation=exports.flexdbIndexInfoValidation=exports.flexdbIndexAccessesValidation=exports.flexdbIndexkeyValidation=exports.flexdbTableInfoValidation=exports.flexdbPagerValidation=void 0;const tslib_1=require("tslib"),v=tslib_1.__importStar(require("../../utils/validator")),common=tslib_1.__importStar(require("./validations"));exports.flexdbPagerValidation=Object.assign({},common.commonOutputValidation,{Offset:1,Limit:1,Total:1}),exports.flexdbTableInfoValidation=Object.assign({},common.commonOutputValidation,{TableName:"",Count:1,Size:1,IndexCount:1,IndexSize:1}),exports.flexdbIndexkeyValidation=Object.assign({},common.commonOutputValidation,{Name:"",Direction:""}),exports.flexdbIndexAccessesValidation=Object.assign({},common.commonOutputValidation,{Ops:1,Since:""}),exports.flexdbIndexInfoValidation=Object.assign({},common.commonOutputValidation,{Name:"",Size:1,Keys:v.$arrayOf(exports.flexdbIndexkeyValidation),Accesses:exports.flexdbIndexAccessesValidation,Unique:v.$multiType(!0,null)}),exports.flexdbListTablesOutputValidation=Object.assign({},common.commonOutputValidation,{Tables:v.$multiType(v.$arrayOf(exports.flexdbTableInfoValidation),null),Pager:exports.flexdbPagerValidation}),exports.flexdbCreateTableOutputValidation=Object.assign({},common.commonOutputValidation,{}),exports.flexdbDeleteTableOutputValidation=Object.assign({},common.commonOutputValidation,{}),exports.flexdbQueryOutputValidation=Object.assign({},common.commonOutputValidation,{Data:v.$arrayOf(""),Pager:exports.flexdbPagerValidation}),exports.flexdbPutItemOutputValidation=Object.assign({},common.commonOutputValidation,{InsertedIds:v.$multiType(v.$arrayOf(""),v.$arrayOf(1))}),exports.flexdbUpdateItemOutputValidation=Object.assign({},common.commonOutputValidation,{}),exports.flexdbDeleteItemOutputValidation=Object.assign({},common.commonOutputValidation,{Deleted:1}),exports.flexdbCountOutputValidation=Object.assign({},common.commonOutputValidation,{Count:1}),exports.flexdbDescribeTableOutputValidation=Object.assign({},common.commonOutputValidation,{Indexes:v.$arrayOf(exports.flexdbIndexInfoValidation),IndexNum:1}),exports.flexdbUpdateTableOutputValidation=Object.assign({},common.commonOutputValidation,{}),exports.flexdbRunCommandsOutputValidation=Object.assign({},common.commonOutputValidation,{}),exports.flexdbModifyTableNamesOutputValidation=Object.assign({},common.commonOutputValidation,{}),exports.flexdbDescribeRestoreTablesOutputValidation=Object.assign({},common.commonOutputValidation,{Tables:v.$arrayOf("")}),exports.flexdbDescribeRestoreTaskOutputValidation=Object.assign({},common.commonOutputValidation,{Tasks:v.$arrayOf(common.restoreTaskValidation)}),exports.flexdbRestoreTCBTablesOutputValidation=Object.assign({},common.commonOutputValidation,{FlowId:1}),exports.flexdbDescribeRestoreTimeOutputValidation=Object.assign({},common.commonOutputValidation,{RestoreTimes:v.$arrayOf("")}),exports.flexdbModifyNameSpaceOutputValidation=Object.assign({},common.commonOutputValidation,{FlowId:1});
}(require("licia/lazyImport")(require), require)