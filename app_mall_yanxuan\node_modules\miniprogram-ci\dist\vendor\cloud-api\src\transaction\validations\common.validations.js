!function(require, directRequire){
"use strict";Object.defineProperty(exports,"__esModule",{value:!0}),exports.tcbDomainInfoValidation=exports.tcbDomainConfigValidation=exports.hostingDomainValidation=exports.tcbRefererValidation=exports.tcbRefererRuleValidation=exports.forceRedirectValidation=exports.ipFreqLimitValidation=exports.ipFilterValidation=exports.tcbHeaderValidation=exports.tcbHeaderRuleValidation=exports.tcbHttpsValidation=exports.tcbCertInfoValidation=exports.tcbStaticValidation=exports.tcbCacheValidation=exports.tcbAuthenticationValidation=exports.tcbOriginValidation=exports.postpayEnvQuotaValidation=exports.staticStoreInfoValidation=exports.routingConfigValidation=exports.versionMatchValidation=exports.versionWeightValidation=exports.functionVersionValidation=exports.loginConfigItemValidation=exports.tableRestoreTimeValidation=exports.restoreTaskValidation=exports.restoreTableTimeRangeValidation=exports.modifyTableNamesInfoValidation=exports.priceInfoValidation=exports.freequotaInfoValidation=exports.restoreHistoryItemValidation=exports.dauStatDataValidation=exports.monitorResourceValidation=exports.monitorPolicyInfoValidation=exports.describePolicyRuleValidation=exports.monitorConditionInfoValidation=exports.voucherApplication=exports.voucherValidation=exports.recoverJobStatusValidation=exports.recoverResultValidation=exports.logServiceExceptionValidation=exports.storageExceptionValidation=exports.limitInfoValidation=exports.userInfoValidation=exports.envInfoValidation=exports.logServiceInfoValidation=exports.functionInfoValidation=exports.storageInfoValidation=exports.databaseInfoValidation=exports.commonOutputValidation=exports.noValidation=void 0,exports.triggerActionValidation=exports.triggerConditionValidation=exports.triggerTypeParameterValidation=exports.triggerConditionTypeParameterValidation=exports.triggerActionTypeParameterValidation=exports.auditDetailInfoValidation=exports.auditRuleInfoValidation=exports.auditTypeInfoValidation=exports.logResObjectValidation=exports.logObjectValidation=exports.postpaidPackageDealInfoValidation=exports.smsRecordValidation=exports.recordValidation=exports.smsFreeQuotaValidation=exports.monitorPeriodValidation=exports.cloudRunServiceSimpleVersionSnapshotValidation=exports.cloudBaseRunServiceHistoryActionValidation=exports.cloudBaseRunServiceHistoryActionInfoValidation=exports.extensionInfoValidation=exports.userExtensionInfoValidation=exports.extensionTaskStatusValidation=exports.upgradeResItemValidation=exports.postpayQuotaLimitValidation=exports.envPostpayPackageInfoValidation=exports.dianshiProductValidation=exports.postpayPackageInfoValidation=exports.certificatesValidation=exports.projectInfoValidation=exports.certificateExtraValidation=exports.cloudBaseCodeBranchValidation=exports.cloudBaseRunVersionFlowItemValidation=exports.cloudBaseRunBuildLogValidation=exports.cloudBaseRunBuildStagesValidation=exports.cloudBaseGwapiValidation=exports.cloudRunExceptionAdviceValidation=exports.cloudRunExceptionLinkInfoValidation=exports.kvPairValidation=exports.cloudBaseRunVersionPodValidation=exports.cloudBaseRunServerVersionItemValidation=exports.objectKvValidation=exports.cloudBaseRunKvPriorityValidation=exports.cloudBaseRunVolumeMountValidation=exports.cloudBaseRunNfsVolumeSourceValidation=exports.cloudBaseRunImageSecretInfoValidation=exports.cloudBaseCodeRepoDetailValidation=exports.cloudBaseCodeRepoNameValidation=exports.cloudBaseRunImageInfoValidation=exports.cloudBaseRunContainerStandardValidation=exports.cloudBaseRunServerItemValidation=exports.cloudBaseRunVpcSubnetValidation=void 0,exports.triggerActionSCFValidation=exports.versionProvisionedConcurrencyInfoValidation=exports.envExtensionItemValidation=exports.tagValidation=exports.triggerConfigValidation=void 0;const tslib_1=require("tslib"),v=tslib_1.__importStar(require("../../utils/validator")),noValidation=()=>!0;exports.noValidation=noValidation,exports.commonOutputValidation={RequestId:v.$optional("")},exports.databaseInfoValidation={InstanceId:"",Status:"",Region:""},exports.storageInfoValidation={Region:"",Bucket:"",CdnDomain:"",AppId:""},exports.functionInfoValidation={Namespace:"",Region:""},exports.logServiceInfoValidation={LogsetName:"",LogsetId:"",TopicName:"",TopicId:"",Region:""},exports.envInfoValidation={EnvId:"",Source:"",Alias:"",CreateTime:"",UpdateTime:"",Status:"",Databases:v.$arrayOf(exports.databaseInfoValidation),Storages:v.$arrayOf(exports.storageInfoValidation),Functions:v.$arrayOf(exports.functionInfoValidation),PackageId:v.$optional(""),PackageName:v.$optional(""),LogServices:v.$optional(v.$arrayOf(exports.logServiceInfoValidation))},exports.userInfoValidation={OpenId:"",GrantUserInfo:!0,NickName:"",Country:"",Province:"",City:"",Gender:1,Language:"",AvatarUrl:"",CreateTime:"",UpdateTime:""},exports.limitInfoValidation={MaxSize:1,TimeUnit:""},exports.storageExceptionValidation={Bucket:"",COSStatus:"",COSRecoverJobId:v.$optional("")},exports.logServiceExceptionValidation={LogsetName:"",Status:"",FunctionUpdateJobId:v.$optional("")},exports.recoverResultValidation={Result:"",ErrorMessage:v.$optional(""),RecoverJobId:v.$optional("")},exports.recoverJobStatusValidation={JobId:"",Status:"",ErrorMessage:v.$optional("")},exports.voucherValidation={VoucherId:"",OwnerUin:"",Amount:1,LeftAmount:1,UseDeadLine:"",Status:v.$optional(1),BaseAmount:1,Reuse:v.$optional(1)},exports.voucherApplication={ApplicationId:"",VoucherPlan:1,Status:"",ProductName:""},exports.monitorConditionInfoValidation={EnvId:"",PolicyId:1,ConditionId:1,Metrics:"",Cmp:"",Threshold:1,Period:1,PeriodNum:1,Convergence:1,Source:"",IsEdit:1},exports.describePolicyRuleValidation={ResType:"",RecGroup:"",RecUser:"",Object:"",Name:"",PolicyId:1},exports.monitorPolicyInfoValidation={Name:"",Note:"",Convergence:1,PolicyId:1,ResType:"",ResName:"",Objects:v.$optional(v.$arrayOf("")),Source:v.$optional(""),ActiveStartTime:v.$optional(""),ActiveEndTime:v.$optional(""),AlertChannels:v.$optional(v.$arrayOf("")),RecGroups:v.$optional(v.$arrayOf("")),RecUsers:v.$optional(v.$arrayOf("")),IsActive:v.$optional(1),IsEdit:v.$optional(1)},exports.monitorResourceValidation={Name:"",Index:v.$arrayOf(""),Period:v.$arrayOf(1),EnIndex:v.$arrayOf(""),EnName:"",IndexUnit:v.$arrayOf(""),Convergence:v.$arrayOf(1),ConvergenceName:v.$arrayOf(""),PeriodNum:v.$arrayOf(1)},exports.dauStatDataValidation={StatDate:"",Amount:1},exports.restoreHistoryItemValidation={OperateTime:"",RestoreTime:"",RestorTables:v.$arrayOf("")},exports.freequotaInfoValidation={ResourceType:"",ResourceMetric:"",FreeQuota:1,MetricUnit:"",DeductType:v.$optional(""),FreeQuotaType:v.$optional("")},exports.priceInfoValidation={ResourceType:"",ResourceMetric:"",UnitPrice:1,MetricUnit:""},exports.modifyTableNamesInfoValidation={OldTableName:"",NewTableName:""},exports.restoreTableTimeRangeValidation={BeginTimestamp:"",EndTimestamp:""},exports.restoreTaskValidation={Tables:v.$optional(v.$arrayOf(exports.modifyTableNamesInfoValidation)),CreateTime:v.$optional(""),Status:v.$optional(1),EndTime:v.$optional(""),RestoreTime:v.$optional("")},exports.tableRestoreTimeValidation={Table:"",TimeRange:v.$arrayOf(exports.restoreTableTimeRangeValidation)},exports.loginConfigItemValidation={Platform:"",PlatformId:"",CreateTime:"",UpdateTime:"",Status:"",Id:""},exports.functionVersionValidation={Version:"",Description:v.$optional(""),AddTime:v.$optional(""),ModTime:v.$optional("")},exports.versionWeightValidation={Version:"",Weight:1},exports.versionMatchValidation={Version:"",Key:"",Method:"",Expression:""},exports.routingConfigValidation={AdditionalVersionWeights:v.$arrayOf(exports.versionWeightValidation),AddtionVersionMatchs:v.$arrayOf(exports.versionMatchValidation)},exports.staticStoreInfoValidation={EnvId:v.$optional(""),CdnDomain:v.$optional(""),Bucket:v.$optional(""),Regoin:v.$optional(""),Status:v.$optional("")},exports.postpayEnvQuotaValidation={ResourceType:"",MetricName:"",Value:1,StartTime:"",EndTime:""},exports.tcbOriginValidation={Master:"",Slave:v.$optional("")},exports.tcbAuthenticationValidation={Switch:"",SecretKey:v.$optional(""),SignParam:v.$optional(""),TimeParam:v.$optional(""),ExpireTime:v.$optional(1)},exports.tcbCacheValidation={RuleType:"",RuleValue:"",CacheTtl:1},exports.tcbStaticValidation={Switch:"",Path:v.$optional("")},exports.tcbCertInfoValidation={HttpsType:1,CertId:""},exports.tcbHttpsValidation={Switch:"",CertInfo:exports.tcbCertInfoValidation},exports.tcbHeaderRuleValidation={HeaderName:"",HeaderValue:v.$arrayOf("")},exports.tcbHeaderValidation={Switch:"",HeaderRules:v.$optional(v.$arrayOf(exports.tcbHeaderRuleValidation))},exports.ipFilterValidation={Switch:"",FilterType:v.$optional(""),Filters:v.$optional(v.$arrayOf(""))},exports.ipFreqLimitValidation={Switch:"",Qps:v.$optional(1)},exports.forceRedirectValidation={Switch:v.$optional(""),RedirectType:v.$optional(""),RedirectStatusCode:v.$optional(1)},exports.tcbRefererRuleValidation={RefererType:"",Referers:v.$optional(v.$arrayOf("")),AllowEmpty:!0},exports.tcbRefererValidation={Switch:"",RefererRules:v.$optional(v.$arrayOf(exports.tcbRefererRuleValidation))},exports.hostingDomainValidation={Domain:"",CertId:"",Status:"",CName:"",UpdateTime:"",CreateTime:"",DNSStatus:"",StatusMessage:v.$optional("")},exports.tcbDomainConfigValidation={Origin:exports.tcbOriginValidation,CosPrivateAccess:"",Authentication:exports.tcbAuthenticationValidation,Cache:v.$arrayOf(exports.tcbCacheValidation),StaticWeb:v.$optional(exports.tcbStaticValidation),RootAccess:v.$optional(""),SpeedLimit:v.$optional(1),Https:v.$optional(exports.tcbHttpsValidation),RspHeader:v.$optional(exports.tcbHeaderValidation),FollowRedirect:v.$optional(""),IpFilter:v.$optional(exports.ipFilterValidation),IpFreqLimit:v.$optional(exports.ipFreqLimitValidation),ForceRedirect:v.$optional(exports.forceRedirectValidation),Refer:v.$optional(exports.tcbRefererValidation)},exports.tcbDomainInfoValidation={Domain:"",DomainId:1,Status:"",DomainConfig:exports.tcbDomainConfigValidation,CName:""},exports.cloudBaseRunVpcSubnetValidation={Id:v.$optional(""),Cidr:v.$optional(""),Zone:v.$optional(""),Type:v.$optional(""),Target:v.$optional(""),Region:v.$optional(""),Name:v.$optional("")},exports.cloudBaseRunServerItemValidation={ServerName:"",CreatedTime:"",UpdatedTime:"",IsPublic:!0,ImageRepo:"",VpcId:"",ServiceRemark:v.$optional(""),TrafficType:v.$optional(""),AllowDeleteImageRepo:v.$optional(!0),NatIp:v.$optional(v.$arrayOf(""))},exports.cloudBaseRunContainerStandardValidation={Cpus:1,Mems:v.$arrayOf(1)},exports.cloudBaseRunImageInfoValidation={RepositoryName:"",IsPublic:!0,TagName:"",ServerAddr:"",ImageUrl:""},exports.cloudBaseCodeRepoNameValidation={Name:v.$optional(""),FullName:v.$optional("")},exports.cloudBaseCodeRepoDetailValidation={Name:exports.cloudBaseCodeRepoNameValidation,Url:""},exports.cloudBaseRunImageSecretInfoValidation={RegistryServer:"",UserName:"",Password:"",Email:""},exports.cloudBaseRunNfsVolumeSourceValidation={Server:"",Path:"",ReadOnly:!0},exports.cloudBaseRunVolumeMountValidation={Name:"",MountPath:"",ReadOnly:!0,NfsVolumes:v.$arrayOf(exports.cloudBaseRunNfsVolumeSourceValidation)},exports.cloudBaseRunKvPriorityValidation={Key:v.$optional(""),Value:v.$optional(""),Priority:v.$optional(1)},exports.objectKvValidation={Key:"",Value:""},exports.cloudBaseRunServerVersionItemValidation={VersionName:"",Status:v.$optional(""),FlowRatio:1,CreatedTime:v.$optional(""),UpdatedTime:v.$optional(""),BuildId:v.$optional(1),UploadType:v.$optional(""),Remark:v.$optional(""),UrlParam:v.$optional(exports.objectKvValidation),Priority:v.$optional(1),IsDefaultPriority:v.$optional(!0),FlowParams:v.$optional(v.$arrayOf(exports.cloudBaseRunKvPriorityValidation))},exports.cloudBaseRunVersionPodValidation={Webshell:"",PodId:"",PodIp:v.$optional(""),Status:"",CreateTime:v.$optional("")},exports.kvPairValidation={Key:"",Value:""},exports.cloudRunExceptionLinkInfoValidation={Index:1,Type:"",MatchWords:v.$optional(""),Link:""},exports.cloudRunExceptionAdviceValidation={Guideline:v.$optional(v.$arrayOf("")),LinkInfos:v.$optional(v.$arrayOf(exports.cloudRunExceptionLinkInfoValidation))},exports.cloudBaseGwapiValidation={ServiceId:"",APIId:"",Path:"",Type:1,Name:"",CreateTime:1,Custom:"",EnableAuth:v.$optional(!0),EnvId:v.$optional(""),AccessType:v.$optional(1)},exports.cloudBaseRunBuildStagesValidation={JsonData:""},exports.cloudBaseRunBuildLogValidation={Total:v.$optional(1),Delivered:v.$optional(1),Text:v.$optional(""),More:v.$optional(!0)},exports.cloudBaseRunVersionFlowItemValidation={VersionName:v.$optional(""),FlowRatio:v.$optional(1),UrlParam:v.$optional(exports.objectKvValidation),Priority:v.$optional(1),IsDefaultPriority:v.$optional(!0)},exports.cloudBaseCodeBranchValidation={Name:"",IsProtected:v.$optional(!0)},exports.certificateExtraValidation={DomainNumber:v.$optional(""),OriginCertificateId:v.$optional(""),ReplacedBy:v.$optional(""),ReplacedFor:v.$optional(""),RenewOrder:v.$optional("")},exports.projectInfoValidation={ProjectName:v.$optional(""),ProjectCreatorUin:v.$optional(1),ProjectCreateTime:v.$optional(""),ProjectResume:v.$optional(""),OwnerUin:v.$optional(1),ProjectId:v.$optional("")},exports.certificatesValidation={OwnerUin:v.$optional(""),ProjectId:v.$optional(""),From:v.$optional(""),PackageType:v.$optional(""),CertificateType:v.$optional(""),ProductZhName:v.$optional(""),Domain:v.$optional(""),Alias:v.$optional(""),Status:v.$optional(1),CertificateExtra:v.$optional(exports.certificateExtraValidation),VulnerabilityStatus:v.$optional(""),StatusMsg:v.$optional(""),VerifyType:v.$optional(""),CertBeginTime:v.$optional(""),CertEndTime:v.$optional(""),ValidityPeriod:v.$optional(""),InsertTime:v.$optional(""),CertificateId:v.$optional(""),SubjectAltName:v.$optional(v.$arrayOf("")),PackageTypeName:v.$optional(""),StatusName:v.$optional(""),IsVip:v.$optional(!0),IsDv:v.$optional(!0),IsWildcard:v.$optional(!0),IsVulnerability:v.$optional(!0),RenewAble:v.$optional(!0),ProjectInfo:v.$optional(exports.projectInfoValidation),BoundResource:v.$optional(v.$arrayOf("")),Deployable:v.$optional(!0)},exports.postpayPackageInfoValidation={PostpayPackageId:"",ResourceType:"",Pid:1,Name:"",Desc:v.$optional(""),Detail:""},exports.dianshiProductValidation={ProductId:1,ProductName:"",PostpayPackageId:"",TimeSpan:1},exports.envPostpayPackageInfoValidation={PostpayPackageId:"",EnvId:"",CreateTime:"",ExpireTime:"",Status:1,Detail:""},exports.postpayQuotaLimitValidation={ResourceType:"",Metric:"",Quota:1,Interval:"",WarningRates:v.$arrayOf(1),CreateTime:"",UpdateTime:"",Status:""},exports.upgradeResItemValidation={CurrentExtensionId:"",NextExtensionId:"",Version:"",ChangeLog:""},exports.extensionTaskStatusValidation={ExtensionId:"",Status:"",Detail:"",Percent:1},exports.userExtensionInfoValidation={Id:"",Status:"",UpdateTime:"",CreateTime:"",TaskInfo:""},exports.extensionInfoValidation={Id:"",Name:"",Version:"",Tags:v.$arrayOf(""),Description:"",CreateTime:"",IconUrl:"",DocUrl:"",Status:"",UpdateTime:"",TemplateId:v.$optional("")},exports.cloudBaseRunServiceHistoryActionInfoValidation={ServerName:v.$optional(v.$arrayOf("")),VersionName:v.$optional(v.$arrayOf("")),RunId:v.$optional(""),ActionType:v.$optional(""),Status:v.$optional(""),ActionData:v.$optional(""),CreatedTime:v.$optional(""),OperatorRemark:v.$optional(""),OperatorType:v.$optional(""),OldData:v.$optional(""),FreshData:v.$optional("")},exports.cloudBaseRunServiceHistoryActionValidation={ActionDetail:v.$optional(exports.cloudBaseRunServiceHistoryActionInfoValidation),StepDetails:v.$optional(v.$arrayOf(exports.cloudBaseRunServiceHistoryActionInfoValidation))},exports.cloudRunServiceSimpleVersionSnapshotValidation={VersionName:v.$optional(""),Remark:v.$optional(""),Cpu:v.$optional(1),Mem:v.$optional(1),MinNum:v.$optional(1),MaxNum:v.$optional(1),ImageUrl:v.$optional(""),PolicyType:v.$optional(""),PolicyThreshold:v.$optional(1),EnvParams:v.$optional(""),ContainerPort:v.$optional(1),CreateTime:v.$optional(""),UpdateTime:v.$optional(""),UploadType:v.$optional(""),DockerfilePath:v.$optional(""),BuildDir:v.$optional(""),RepoType:v.$optional(""),Repo:v.$optional(""),Branch:v.$optional(""),EnvId:v.$optional(""),ServerName:v.$optional(""),PackageName:v.$optional(""),PackageVersion:v.$optional(""),CustomLogs:v.$optional(""),InitialDelaySeconds:v.$optional(1),SnapshotName:v.$optional(""),ImageInfo:v.$optional(exports.cloudBaseRunImageInfoValidation),CodeDetail:v.$optional(exports.cloudBaseCodeRepoDetailValidation),Status:v.$optional("")},exports.monitorPeriodValidation={IndexName:v.$optional(""),Period:v.$optional(v.$arrayOf(1))},exports.smsFreeQuotaValidation={FreeQuota:v.$optional(1),TotalUsedQuota:v.$optional(1),CycleStart:v.$optional(""),CycleEnd:v.$optional(""),TodayUsedQuota:v.$optional(1)},exports.recordValidation={StateTime:"",ReqCnt:1,FeeCnt:1,SuccCnt:1},exports.smsRecordValidation={Mobile:v.$optional(""),Content:v.$optional(""),ContentSize:v.$optional(1),Fee:v.$optional(1),CreateTime:v.$optional(""),ReceivedTime:v.$optional(""),Status:v.$optional(""),Remarks:v.$optional("")},exports.postpaidPackageDealInfoValidation={TranId:"",DealOwner:"",CreateTime:"",DealStatus:1,DealCost:1,EnvId:"",PayTime:"",TimeSpan:1,Price:1,TimeUnit:"",RefundAmount:1,VoucherDecline:v.$optional(1),Action:v.$optional(""),OriginTotalCost:v.$optional(1),PostpaidPackageId:v.$optional(""),ProductCode:v.$optional(""),SubProductCode:v.$optional(""),Detail:v.$optional(""),ResourceIds:v.$optional(v.$arrayOf(""))},exports.logObjectValidation={TopicId:"",TopicName:"",Timestamp:"",Content:"",FileName:"",Source:""},exports.logResObjectValidation={Context:"",ListOver:!0,Results:v.$arrayOf(exports.logObjectValidation)},exports.auditTypeInfoValidation={AuditType:v.$optional(""),ShieldedLine:1},exports.auditRuleInfoValidation={ResourceType:v.$optional(""),AuditKey:v.$optional(""),AuditSubKey:v.$optional(""),RewriteComment:v.$optional(""),RewriteSwitch:v.$optional(1),EnvId:v.$optional(""),Id:v.$optional(1),AuditType:v.$optional(v.$arrayOf(exports.auditTypeInfoValidation)),UpdateRewriteSwitch:v.$optional(!0),ResourceName:v.$optional(""),Region:v.$optional(""),Uin:v.$optional("")},exports.auditDetailInfoValidation={AuditKey:v.$optional(""),AuditSubKey:v.$optional(""),AuditType:v.$optional(v.$arrayOf("")),AuditTime:v.$optional(""),AuditResult:v.$optional(""),AuditScore:v.$optional(1),AuditComment:v.$optional(""),TriggerEvent:v.$optional(""),EnvId:v.$optional(""),ResultId:v.$optional(""),AuditDesc:v.$optional(""),DocId:v.$optional("")},exports.triggerActionTypeParameterValidation={Type:v.$optional(""),Text:v.$optional("")},exports.triggerConditionTypeParameterValidation={ConditionOperator:v.$optional(""),ConditionTypes:v.$optional(v.$arrayOf(""))},exports.triggerTypeParameterValidation={ConditionTypes:v.$optional(v.$arrayOf(exports.triggerConditionTypeParameterValidation)),TriggerType:v.$optional(""),TriggerTypeZh:v.$optional(""),ActionTypes:v.$optional(v.$arrayOf(exports.triggerActionTypeParameterValidation)),RateLimit:v.$optional(v.$arrayOf(exports.triggerActionTypeParameterValidation)),Event:v.$optional(v.$arrayOf(""))},exports.triggerConditionValidation={ConditionKey:"",ConditionOperator:"",ConditionType:"",ConditionValue:"",TriggerId:v.$optional(1)},exports.triggerActionValidation={ActionType:"",ActionKey:"",TriggerId:v.$optional(1),Template:v.$optional(""),RateLimit:v.$optional(""),RateInterval:v.$optional(1)},exports.triggerConfigValidation={Id:v.$optional(1),Uin:v.$optional(""),EnvId:v.$optional(""),TriggerType:v.$optional(""),TriggerKey:v.$optional(""),TriggerEvent:v.$optional(""),TriggerMode:v.$optional(""),Status:v.$optional(""),CreateTime:v.$optional(""),UpdateTime:v.$optional(""),Conditions:v.$optional(v.$arrayOf(exports.triggerConditionValidation)),Actions:v.$optional(v.$arrayOf(exports.triggerActionValidation)),TriggerName:v.$optional(""),TriggerDesc:v.$optional(""),ResourceName:v.$optional(""),Region:v.$optional("")},exports.tagValidation={Key:"",Value:""},exports.envExtensionItemValidation={EnvId:"",ExtensionId:"",Status:"",TaskInfo:"",CreateTime:""},exports.versionProvisionedConcurrencyInfoValidation={AllocatedProvisionedConcurrencyNum:1,AvailableProvisionedConcurrencyNum:1,Status:"",StatusReason:"",Qualifier:""},exports.triggerActionSCFValidation={TriggerName:v.$optional(""),TriggerProvisionedConcurrencyNum:v.$optional(1),TriggerCronConfig:v.$optional(""),ProvisionedType:v.$optional("")};
}(require("licia/lazyImport")(require), require)