!function(require, directRequire){
"use strict";Object.defineProperty(exports,"__esModule",{value:!0}),exports.dateToStr=exports.strToDate=void 0;const tslib_1=require("tslib"),moment_1=tslib_1.__importDefault(require("moment"));function strToDate(t){return(0,moment_1.default)(t).utcOffset(480,!0).toDate().getTime()}function dateToStr(t,e="YYYY-MM-DD HH:mm:ss"){return(0,moment_1.default)(t).utcOffset(480,!0).format(e)}moment_1.default.locale("zh-cn"),exports.strToDate=strToDate,exports.dateToStr=dateToStr;
}(require("licia/lazyImport")(require), require)